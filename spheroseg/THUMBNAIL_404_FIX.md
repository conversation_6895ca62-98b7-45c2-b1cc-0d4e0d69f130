# Thumbnail 404 Fix Documentation

## Problem
The production server at spherosegapp.utia.cas.cz shows 404 errors for thumbnails and avatars because the nginx configuration doesn't proxy `/uploads/` requests to the backend service.

## Root Cause
The production nginx server is not using the configuration from `nginx/prod.conf` in this repository, which correctly proxies `/uploads/` to the backend. The local configuration works correctly, but production is missing this critical proxy configuration.

## Solution Implemented

### API Workaround
Since we don't have SSH access to the production server to update nginx configuration, we implemented a workaround:

1. **Backend API Route** (`/api/static/uploads/*`):
   - Created a new route in `packages/backend/src/routes/static.ts`
   - Serves static files from the uploads directory through the API
   - Maintains proper content types and caching headers

2. **Frontend URL Construction**:
   - Modified `packages/frontend/src/lib/urlUtils.ts` to detect production environment
   - Routes all `/uploads/` URLs through `/api/static/uploads/` on production
   - Preserves direct URLs for development environment

3. **Image Error Handling**:
   - Updated `packages/frontend/src/components/project/ImageDisplay.tsx`
   - Falls back to API static route when direct URLs fail on production

## Production Nginx Fix (Preferred Solution)

If you have access to the production server, update the nginx configuration to include:

```nginx
# Uploads handling (including avatars)
location /uploads/ {
    proxy_pass http://spheroseg-backend:5001/uploads/;
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Cache static files
    proxy_cache_valid 200 1d;
    proxy_cache_valid 404 1m;
}
```

Once the nginx configuration is updated on production, the API workaround can be removed by reverting the changes to `urlUtils.ts` and `ImageDisplay.tsx`.

## Testing

### Local Testing
1. Build and run the production profile: `docker-compose --profile prod up -d`
2. Access http://localhost and verify thumbnails load correctly
3. Check browser console for any 404 errors

### Production Testing
1. Access https://spherosegapp.utia.cas.cz
2. Navigate to a project with images
3. Verify thumbnails and avatars load without 404 errors
4. Check browser console logs for "Routing through API for production" messages

## Files Modified

1. `/packages/backend/src/routes/static.ts` - New API route for static files
2. `/packages/backend/src/routes/index.ts` - Route registration
3. `/packages/frontend/src/lib/urlUtils.ts` - Production URL routing logic
4. `/packages/frontend/src/components/project/ImageDisplay.tsx` - Error fallback handling

## Rollback

To rollback this change:
1. Remove the production detection logic from `urlUtils.ts`
2. Remove the production fallback from `ImageDisplay.tsx`
3. Remove the static route from backend
4. Rebuild and deploy