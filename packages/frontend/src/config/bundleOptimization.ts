/**
 * Bundle Optimization Configuration
 * 
 * Centralized configuration for bundle size thresholds, chunk splitting,
 * and performance optimization settings.
 */

// Bundle size thresholds (in bytes)
export const BUNDLE_SIZE_THRESHOLDS = {
  // Warning threshold - when to show warnings about bundle size
  WARNING: 244 * 1024, // 244kb
  
  // Error threshold - when to fail builds due to bundle size
  ERROR: 500 * 1024,   // 500kb
  
  // Individual chunk size limits
  CHUNK_WARNING: 100 * 1024, // 100kb
  CHUNK_ERROR: 200 * 1024,   // 200kb
  
  // Vendor chunk size limits
  VENDOR_WARNING: 300 * 1024, // 300kb
  VENDOR_ERROR: 600 * 1024,   // 600kb
} as const;

// Performance monitoring thresholds
export const PERFORMANCE_THRESHOLDS = {
  // Chunk load time thresholds (in milliseconds)
  CHUNK_LOAD_WARNING: 3000,  // 3 seconds
  CHUNK_LOAD_ERROR: 5000,    // 5 seconds
  
  // Component render time thresholds
  RENDER_WARNING: 16,        // 16ms (60fps)
  RENDER_ERROR: 33,          // 33ms (30fps)
  
  // Memory usage thresholds (in MB)
  MEMORY_WARNING: 50,        // 50MB
  MEMORY_ERROR: 100,         // 100MB
} as const;

// Code splitting configuration
export const CODE_SPLITTING_CONFIG = {
  // Cache sizes
  COMPONENT_CACHE_SIZE: 50,
  PROMISE_CACHE_SIZE: 30,
  
  // Retry configuration
  DEFAULT_RETRY_ATTEMPTS: 3,
  DEFAULT_RETRY_DELAY: 1000, // 1 second
  MAX_RETRY_DELAY: 8000,     // 8 seconds
  
  // Prefetch configuration
  PREFETCH_DELAY: 100,       // 100ms between prefetches
  IDLE_CALLBACK_TIMEOUT: 1,  // 1ms for requestIdleCallback fallback
} as const;

// Vendor chunk configuration
export const VENDOR_CHUNKS = {
  // Core React ecosystem
  react: ['react', 'react-dom', 'react-router-dom'],
  
  // UI libraries
  ui: ['@radix-ui', '@headlessui', 'framer-motion'],
  
  // Data fetching and state
  data: ['@tanstack/react-query', 'axios', 'socket.io-client'],
  
  // Utilities
  utils: ['lodash', 'date-fns', 'uuid'],
  
  // Visualization
  viz: ['recharts', 'd3', 'konva'],
} as const;

// Route prefetch configuration
export const ROUTE_PREFETCH_CONFIG = [
  {
    routes: ['/dashboard', '/projects/:id'],
    priority: 'high' as const,
    strategy: 'idle' as const,
  },
  {
    routes: ['/settings', '/profile'],
    priority: 'low' as const,
    strategy: 'hover' as const,
  },
  {
    routes: ['/documentation', '/about'],
    priority: 'low' as const,
    strategy: 'visible' as const,
  },
] as const;

// Heavy component configuration
export const HEAVY_COMPONENTS = {
  // Segmentation editor components
  SEGMENTATION_CANVAS: {
    chunkName: 'segmentation-canvas',
    prefetch: true,
    path: '../pages/segmentation/components/canvas/CanvasContainer',
  },
  
  // Export components
  EXCEL_EXPORTER: {
    chunkName: 'excel-exporter',
    prefetch: false,
    path: '../pages/segmentation/components/project/export/ExcelExporter',
  },
  
  // Analytics dashboard
  ANALYTICS_DASHBOARD: {
    chunkName: 'analytics-dashboard',
    prefetch: false,
    path: '../components/analytics/AnalyticsDashboardOptimized',
  },
  
  // Image gallery with virtual scrolling
  VIRTUAL_IMAGE_GRID: {
    chunkName: 'virtual-image-grid',
    prefetch: true,
    path: '../components/project/VirtualImageGrid',
  },
} as const;

// Environment-specific configuration
export const getEnvironmentConfig = () => {
  const isDevelopment = import.meta.env.DEV;
  const isProduction = import.meta.env.PROD;
  
  return {
    // Enable more aggressive optimizations in production
    enableChunkSplitting: isProduction,
    enablePrefetching: isProduction,
    enablePerformanceMonitoring: true,
    
    // Development-specific settings
    enableHotReload: isDevelopment,
    enableSourceMaps: isDevelopment,
    
    // Logging levels
    logLevel: isDevelopment ? 'debug' : 'warn',
    
    // Bundle analysis
    enableBundleAnalysis: process.env.ANALYZE === 'true',
  };
};

// Export all configurations as a single object for convenience
export const bundleOptimizationConfig = {
  thresholds: BUNDLE_SIZE_THRESHOLDS,
  performance: PERFORMANCE_THRESHOLDS,
  codeSplitting: CODE_SPLITTING_CONFIG,
  vendorChunks: VENDOR_CHUNKS,
  routePrefetch: ROUTE_PREFETCH_CONFIG,
  heavyComponents: HEAVY_COMPONENTS,
  environment: getEnvironmentConfig(),
} as const;

export default bundleOptimizationConfig;
