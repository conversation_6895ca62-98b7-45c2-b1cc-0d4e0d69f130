/**
 * API v1 Routes
 *
 * This file defines all routes for version 1 of the API.
 * It consolidates all functionality into a single, versioned entry point.
 */

import express, { Router } from 'express';
import authRoutes from '../auth';
import userRoutes from '../users';
import projectRoutes from '../projects';
import imageRoutes from '../images';
import segmentationRoutes from '../segmentation';
import userStatsRoutes from '../userStats';
import userProfileRoutes from '../userProfile';
import projectSharesRoutes from '../projectShares';
import metricsRoutes from '../metrics';
import statusRoutes from '../status';
import previewRoutes from '../preview';
import logsRoutes from '../logs';
import performanceRoutes from '../performance';
import accessRequestsRoutes from '../accessRequests';
import adminRoutes from '../admin';
import downloadRoutes from '../download';
import diagnosticsRoutes from '../diagnostics';
import securityReportRoutes from '../securityReportRoutes';

const router: Router = express.Router();

// API versioning middleware
router.use((req, res, next) => {
  res.setHeader('API-Version', 'v1');
  next();
});

// Register all v1 routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/user-stats', userStatsRoutes);
router.use('/user-profile', userProfileRoutes);
router.use('/projects', projectRoutes);
router.use('/images', imageRoutes);
router.use('/segmentation', segmentationRoutes);
router.use('/project-shares', projectSharesRoutes);
router.use('/metrics', metricsRoutes);
router.use('/status', statusRoutes);
router.use('/preview', previewRoutes);
router.use('/logs', logsRoutes);
router.use('/performance', performanceRoutes);
router.use('/access-requests', accessRequestsRoutes);
router.use('/admin', adminRoutes);
router.use('/download', downloadRoutes);
router.use('/diagnostics', diagnosticsRoutes);
router.use('/security-reports', securityReportRoutes);

// Version info endpoint
router.get('/version', (req, res) => {
  res.json({
    success: true,
    version: 'v1',
    apiVersion: '1.0.0',
    status: 'stable',
    timestamp: new Date().toISOString(),
  });
});

export default router;
