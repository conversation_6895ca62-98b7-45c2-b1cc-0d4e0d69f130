/**
 * API Routes - Consolidated and Clean Architecture
 *
 * This file exports the main API router with proper versioning support.
 * All routes are now properly organized under /api/v1/* structure.
 * Legacy routes have been removed to prevent conflicts.
 */

import express, { Router } from 'express';
import v1Routes from './v1';
import healthRoutes from './health'; // Consolidated health checks

// Create main router
const router: Router = express.Router();

// Mount version 1 routes at /v1
router.use('/v1', v1Routes);

// Mount health check routes at /health
router.use('/health', healthRoutes);

// API documentation endpoint
router.get('/docs', (req, res) => {
  res.json({
    name: 'SpheroSeg API',
    version: '1.0.0',
    description: 'API for medical image segmentation platform',
    endpoints: {
      v1: '/api/v1',
      health: '/api/health',
      docs: '/api/docs',
    },
    contact: {
      name: 'SpheroSeg Team',
      email: '<EMAIL>',
    },
  });
});

// Catch-all for undefined API routes
router.use('*', (req, res) => {
  res.status(404).json({
    error: 'API endpoint not found',
    message: `The endpoint ${req.method} ${req.originalUrl} does not exist`,
    suggestion: 'Check the API documentation at /api/docs',
  });
});

export default router;

/**
 * Setup all routes for the Express application
 * @param app - Express application instance
 */
export const setupRoutes = (app: express.Application) => {
  // Mount the API router at /api
  app.use('/api', router);
};
