server {
    listen 80;
    server_name spherosegapp.utia.cas.cz localhost;

    # Let's Encrypt verification
    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt;
        try_files $uri =404;
    }

    # Redirect HTTP to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name spherosegapp.utia.cas.cz localhost;

    # SSL configuration - Let's Encrypt
    ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz-0001/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz-0001/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;

    # Improve SSL security
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # Add security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";

    # HSTS (enabled with valid certificate)
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

    # Enable Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/json application/xml+rss application/rss+xml application/atom+xml image/svg+xml;
    gzip_disable "MSIE [1-6]\.";

    # Enable Brotli compression (if module available)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css text/xml text/javascript application/javascript application/json application/xml+rss application/rss+xml application/atom+xml image/svg+xml;

    # Set proper MIME types
    types {
        application/javascript js;
        application/javascript mjs; 
        application/javascript ts;
    }
    
    # Frontend production proxy
    location / {
        proxy_pass http://spheroseg-frontend-prod:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # Socket.IO WebSocket proxy - MUST be before /api
    location /socket.io/ {
        proxy_pass http://spheroseg-backend:5001/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket specific
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
        proxy_request_buffering off;
        proxy_buffering off;
    }

    # Static files through API (for production workaround)
    location /api/static/ {
        proxy_pass http://spheroseg-backend:5001/api/static/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Increase buffer size for file uploads
        client_max_body_size 100M;
        proxy_request_buffering off;
        proxy_buffering off;

        # Cache static files appropriately
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
        add_header X-Content-Type-Options nosniff;

        # Add CORS headers for cross-origin requests
        add_header Access-Control-Allow-Origin $http_origin always;
        add_header Access-Control-Allow-Credentials true always;
    }

    # Direct API uploads proxy (for /api/uploads/ paths)
    location /api/uploads/ {
        proxy_pass http://spheroseg-backend:5001/uploads/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Cache static files appropriately
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
        add_header X-Content-Type-Options nosniff;

        # Add CORS headers for cross-origin requests
        add_header Access-Control-Allow-Origin $http_origin always;
        add_header Access-Control-Allow-Credentials true always;
    }

    # Backend API proxy
    location /api {
        proxy_pass http://spheroseg-backend:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Forward authorization header
        proxy_set_header Authorization $http_authorization;
        proxy_pass_header Authorization;

        # Increase timeouts for API calls
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # Increase buffer size for file uploads
        client_max_body_size 100M;
        proxy_request_buffering off;
        proxy_buffering off;
    }

    # Specific API endpoints for segmentations
    location /api/segmentations {
        proxy_pass http://spheroseg-backend:5001/api/segmentations;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase timeouts for segmentation API calls
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;

        # Increase buffer size for file uploads
        client_max_body_size 200M;
        proxy_request_buffering off;
        proxy_buffering off;
    }

    # Specific API endpoints for projects
    location /api/projects {
        proxy_pass http://spheroseg-backend:5001/api/projects;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase timeouts for project API calls
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;

        # Increase buffer size for file uploads
        client_max_body_size 200M;
        proxy_request_buffering off;
        proxy_buffering off;
    }

    # WebSocket proxy for HMR (Hot Module Replacement) - not needed in prod
    # location /@hmr {
    #     proxy_pass http://spheroseg-frontend-prod:3000;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "upgrade";
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto https;
    #     proxy_set_header X-Forwarded-Host $host;
    #     proxy_set_header X-Forwarded-Port $server_port;
    #     proxy_read_timeout 300s;
    #     proxy_connect_timeout 75s;
    # }

    # Assets proxy - serve from frontend-prod container
    location /assets/ {
        proxy_pass http://spheroseg-frontend-prod:80/assets/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_cache_bypass $http_upgrade;
        
        # Long cache for static assets
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # Enable compression for assets
        gzip_static on;
        
        # Set proper expiry headers
        expires 1y;
        access_log off;
    }

    # Uploads handling (including avatars)
    location /uploads/ {
        proxy_pass http://spheroseg-backend:5001/uploads/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase buffer size for file uploads
        client_max_body_size 100M;
        proxy_request_buffering off;
        proxy_buffering off;
        
        # Add CORS headers for avatars
        add_header Access-Control-Allow-Origin $http_origin always;
        add_header Access-Control-Allow-Credentials true always;
    }

    # Specific location for manifest.json - serve from frontend
    location = /manifest.json {
        proxy_pass http://spheroseg-frontend-prod:80/manifest.json;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Cache manifest appropriately
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
        add_header X-Content-Type-Options nosniff;
    }

    # Cache static file types
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|otf|eot)$ {
        proxy_pass http://spheroseg-frontend-prod:80;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Long cache for static files
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # Enable compression
        gzip_static on;
        access_log off;
    }

    # Cache JSON and other API responses appropriately (but not manifest.json)
    location ~* (?<!manifest)\.(json)$ {
        proxy_pass http://spheroseg-backend:5001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Short cache for API responses
        expires 5m;
        add_header Cache-Control "public, must-revalidate";
    }
}
