{"version": "1.0.0", "entries": {"e2e/accessibility/wcag-compliance.spec.ts": {"hash": "ce66f79d7be2c4072441ac9ed93985fc", "timestamp": 1752485314376, "result": "failed", "duration": 1291, "error": "AssertionError: 2 accessibility violations were detected\n\n2 !== 0\n"}, "e2e/auth.spec.ts": {"hash": "d9919548ebf0cc225c636f1a1ee17cb5", "timestamp": 1752482045124, "result": "passed", "duration": 2808}, "e2e/functionality/image-gallery-refresh.spec.ts": {"hash": "6c70de27db209a5b99c0800d23d6b8bb", "timestamp": 1752482205967, "result": "failed", "duration": 31858, "error": "TimeoutError: page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n"}, "e2e/functionality/image-upload.spec.ts": {"hash": "fae9baa0f134aa3c4b657983ca0c59f3", "timestamp": 1752485009854, "result": "failed", "duration": 63046, "error": "TimeoutError: page.fill: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[type=\"email\"]')\u001b[22m\n"}}}