/**
 * Health Check Routes - Single Source of Truth
 *
 * This file provides all health check endpoints in a consolidated manner.
 * Replaces all other health check implementations.
 */

import express, { Request, Response, Router } from 'express';
import pool from '../db';
import logger from '../utils/logger';
import config from '../config';

const router: Router = express.Router();

// GET /api/health - Comprehensive health check
router.get('/', async (req: Request, res: Response) => {
  try {
    // Check database connection
    const dbConnected = await checkDbConnection();

    // Check ML service if required
    const mlServiceStatus = config.segmentation.checkpointPath
      ? 'configured'
      : 'missing_checkpoint';

    // Send health status
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || 'unknown',
      components: {
        api: 'healthy',
        database: dbConnected ? 'connected' : 'disconnected',
        mlService: mlServiceStatus,
      },
      environment: config.env,
    });

    logger.debug('Health check succeeded', {
      database: dbConnected,
      mlService: mlServiceStatus,
    });
  } catch (error) {
    logger.error('Health check failed', { error });

    // Even on error, return 200 with degraded status
    // This allows load balancers to still route traffic
    res.status(200).json({
      status: 'degraded',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || 'unknown',
      components: {
        api: 'healthy',
        database: 'disconnected',
        mlService: 'unknown',
      },
      environment: config.env,
    });
  }
});

// GET /api/health/detailed - Detailed health check for monitoring
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const health = await performDetailedHealthCheck();
    res.status(200).json(health);
  } catch (error) {
    logger.error('Detailed health check failed', { error });
    res.status(503).json({
      status: 'unhealthy',
      error: 'Health check failed',
    });
  }
});

// GET /api/health/ready - Kubernetes readiness probe
router.get('/ready', async (req: Request, res: Response) => {
  try {
    const dbConnected = await checkDbConnection();
    const ready = dbConnected;
    
    res.status(ready ? 200 : 503).json({
      ready,
      database: dbConnected ? 'connected' : 'disconnected',
    });
  } catch (error) {
    res.status(503).json({
      ready: false,
      error: 'Readiness check failed',
    });
  }
});

// GET /api/health/live - Kubernetes liveness probe
router.get('/live', (req: Request, res: Response) => {
  res.status(200).json({
    alive: true,
    timestamp: new Date().toISOString(),
  });
});

// Helper function to check database connection
async function checkDbConnection(): Promise<boolean> {
  try {
    await pool.query('SELECT 1 as connection_test');
    return true;
  } catch (error) {
    logger.error('Database connection check failed', { error });
    return false;
  }
}

// Helper function for detailed health check
async function performDetailedHealthCheck() {
  const checks = await Promise.allSettled([
    checkDbConnection(),
    // Add more checks here as needed
  ]);

  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    checks: {
      database: checks[0].status === 'fulfilled' ? 'healthy' : 'unhealthy',
    },
  };
}

export default router;
