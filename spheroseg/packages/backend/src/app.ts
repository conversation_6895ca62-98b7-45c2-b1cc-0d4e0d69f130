/**
 * Express Application Configuration
 *
 * This module configures the Express application with all middleware and routes.
 * Separated from server.ts for better testing and modularity.
 */

import express, { Application } from 'express';
import { configureMiddleware, configureErrorMiddleware } from './middleware';
import { setupRoutes } from './routes';
import config from './config';
import logger from './utils/logger';

// TODO: Fix i18n import - temporarily disabled
// import i18next from './config/i18n';

/**
 * Create and configure Express application
 */
export const createApp = (): Application => {
  const app = express();

  // Enable trust proxy for proper IP detection behind nginx/docker
  app.set('trust proxy', true);

  // CRITICAL: Add body parser FIRST before any other middleware
  // This ensures the body is parsed before security checks
  app.use(
    express.json({
      limit: '10mb',
      verify: (req: express.Request, res: express.Response, buf: Buffer) => {
        // Store raw body for debugging
        (req as any).rawBody = buf;
      },
    })
  );
  app.use(
    express.urlencoded({
      extended: true,
      limit: '10mb',
    })
  );

  // Configure all middleware in the correct order
  configureMiddleware(app);

  // Setup API routes
  setupRoutes(app);

  // Configure error handling middleware (must be after routes)
  configureErrorMiddleware(app);

  logger.info('Express application configured successfully', {
    environment: config.env,
    corsOrigins: config.server.corsOrigins,
  });

  return app;
};

// Export configured app instance
export default createApp();
