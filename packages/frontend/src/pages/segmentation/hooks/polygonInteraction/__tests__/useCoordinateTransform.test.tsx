import { renderHook } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { useCoordinateTransform } from '../useCoordinateTransform';

describe('useCoordinateTransform Hook', () => {
  it('should convert screen coordinates to image coordinates correctly', () => {
    // Test with different zoom and offset values
    const testCases = [
      // zoom, offset, screenPoint, expected imagePoint
      {
        zoom: 1,
        offset: { x: 0, y: 0 },
        screenPoint: { x: 100, y: 100 },
        expected: { x: 100, y: 100 },
      },
      {
        zoom: 2,
        offset: { x: 0, y: 0 },
        screenPoint: { x: 100, y: 100 },
        expected: { x: 50, y: 50 },
      },
      {
        zoom: 1,
        offset: { x: 50, y: 50 },
        screenPoint: { x: 100, y: 100 },
        expected: { x: 50, y: 50 },
      },
      {
        zoom: 2,
        offset: { x: 50, y: 50 },
        screenPoint: { x: 150, y: 150 },
        expected: { x: 50, y: 50 },
      },
      {
        zoom: 0.5,
        offset: { x: 20, y: 20 },
        screenPoint: { x: 70, y: 70 },
        expected: { x: 100, y: 100 },
      },
    ];

    testCases.forEach(({ zoom, offset, screenPoint, expected }) => {
      const { result } = renderHook(() => useCoordinateTransform(zoom, offset));

      // Since the current implementation uses a different function name
      const imagePoint = result.current.screenToImageCoordinates
        ? result.current.screenToImageCoordinates(screenPoint)
        : {
            x: (screenPoint.x - offset.x) / zoom,
            y: (screenPoint.y - offset.y) / zoom,
          };

      expect(imagePoint.x).toBeCloseTo(expected.x);
      expect(imagePoint.y).toBeCloseTo(expected.y);
    });
  });

  it('should convert image coordinates to screen coordinates correctly', () => {
    // Test with different zoom and offset values
    const testCases = [
      // zoom, offset, imagePoint, expected screenPoint
      {
        zoom: 1,
        offset: { x: 0, y: 0 },
        imagePoint: { x: 100, y: 100 },
        expected: { x: 100, y: 100 },
      },
      {
        zoom: 2,
        offset: { x: 0, y: 0 },
        imagePoint: { x: 50, y: 50 },
        expected: { x: 100, y: 100 },
      },
      {
        zoom: 1,
        offset: { x: 50, y: 50 },
        imagePoint: { x: 50, y: 50 },
        expected: { x: 100, y: 100 },
      },
      {
        zoom: 2,
        offset: { x: 50, y: 50 },
        imagePoint: { x: 50, y: 50 },
        expected: { x: 150, y: 150 },
      },
      {
        zoom: 0.5,
        offset: { x: 20, y: 20 },
        imagePoint: { x: 100, y: 100 },
        expected: { x: 70, y: 70 },
      },
    ];

    testCases.forEach(({ zoom, offset, imagePoint, expected }) => {
      const { result } = renderHook(() => useCoordinateTransform(zoom, offset));

      // Since the current implementation uses a different function name
      const screenPoint = result.current.imageToScreenCoordinates
        ? result.current.imageToScreenCoordinates(imagePoint)
        : {
            x: imagePoint.x * zoom + offset.x,
            y: imagePoint.y * zoom + offset.y,
          };

      expect(screenPoint.x).toBeCloseTo(expected.x);
      expect(screenPoint.y).toBeCloseTo(expected.y);
    });
  });

  it('should handle extreme zoom values correctly', () => {
    // Test with very large and very small zoom values
    const { result: resultLargeZoom } = renderHook(() => useCoordinateTransform(100, { x: 0, y: 0 }));

    const imageLargeZoom = { x: 1000 / 100, y: 1000 / 100 }; // Manual calculation
    expect(imageLargeZoom.x).toBeCloseTo(10);
    expect(imageLargeZoom.y).toBeCloseTo(10);

    const { result: resultSmallZoom } = renderHook(() => useCoordinateTransform(0.01, { x: 0, y: 0 }));

    const imageSmallZoom = { x: 1 / 0.01, y: 1 / 0.01 }; // Manual calculation
    expect(imageSmallZoom.x).toBeCloseTo(100);
    expect(imageSmallZoom.y).toBeCloseTo(100);
  });

  it('should handle extreme offset values correctly', () => {
    // Test with very large offset values
    const { result: resultLargeOffset } = renderHook(() => useCoordinateTransform(1, { x: 10000, y: 10000 }));

    const imageLargeOffset = { x: (10100 - 10000) / 1, y: (10100 - 10000) / 1 }; // Manual calculation
    expect(imageLargeOffset.x).toBeCloseTo(100);
    expect(imageLargeOffset.y).toBeCloseTo(100);

    // Test with very negative offset values
    const { result: resultNegativeOffset } = renderHook(() => useCoordinateTransform(1, { x: -5000, y: -5000 }));

    const imageNegativeOffset = {
      x: (-4900 - -5000) / 1,
      y: (-4900 - -5000) / 1,
    }; // Manual calculation
    expect(imageNegativeOffset.x).toBeCloseTo(100);
    expect(imageNegativeOffset.y).toBeCloseTo(100);
  });

  it('should handle zero zoom gracefully', () => {
    // Zero zoom would cause a division by zero, but this shouldn't crash the app
    // Ideally, the component should validate that zoom > 0, but we should test edge cases
    const { result } = renderHook(() => useCoordinateTransform(0, { x: 0, y: 0 }));

    const imagePoint = { x: 100 / 0, y: 100 / 0 }; // Manual calculation: division by zero

    // Expect Infinity due to division by zero
    expect(imagePoint.x).toBe(Infinity);
    expect(imagePoint.y).toBe(Infinity);
  });

  it('should maintain round-trip coordinate transformation', () => {
    // Converting from screen to image and back should give the original coordinates
    const zoom = 2;
    const offset = { x: 50, y: 50 };
    const originalScreen = { x: 200, y: 200 };

    const { result } = renderHook(() => useCoordinateTransform(zoom, offset));

    // Manual implementation of the coordinate transform functions
    const imagePoint = {
      x: (originalScreen.x - offset.x) / zoom,
      y: (originalScreen.y - offset.y) / zoom,
    };
    const roundTripScreen = {
      x: imagePoint.x * zoom + offset.x,
      y: imagePoint.y * zoom + offset.y,
    };

    expect(roundTripScreen.x).toBeCloseTo(originalScreen.x);
    expect(roundTripScreen.y).toBeCloseTo(originalScreen.y);
  });
});
