#!/usr/bin/env node

/**
 * Test script to verify static file routing fixes
 * 
 * This script tests the various URL patterns that were causing 404 errors:
 * 1. Avatar images: /api/static/uploads/avatars/
 * 2. Thumbnail images: /api/static/uploads/[project-id]/thumb-images-*.png
 * 3. Main images: /uploads/ and /api/uploads/ patterns
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'https://spherosegapp.utia.cas.cz';

// Test URLs that should work after the fixes
// We're testing the routing, not whether specific files exist
const testUrls = [
  // Avatar images - should get proper 404 from backend, not nginx
  '/api/static/uploads/avatars/nonexistent-avatar.jpg',

  // Thumbnail images - should get proper 404 from backend, not nginx
  '/api/static/uploads/sample-project-id/thumb-nonexistent.png',

  // Main images - both patterns should work and get proper 404 from backend
  '/uploads/sample-project-id/nonexistent-image.png',
  '/api/uploads/sample-project-id/nonexistent-image.png',
  '/api/static/uploads/sample-project-id/nonexistent-image.png',

  // Test a route that should definitely work - the main page
  '/',

  // Test API route
  '/api/projects',
];

/**
 * Test a single URL
 */
function testUrl(url) {
  return new Promise((resolve) => {
    const fullUrl = `${BASE_URL}${url}`;
    const protocol = fullUrl.startsWith('https:') ? https : http;
    
    console.log(`Testing: ${url}`);
    
    const req = protocol.get(fullUrl, (res) => {
      const status = res.statusCode;
      const success = status < 400;
      
      console.log(`  Status: ${status} ${success ? '✓' : '✗'}`);
      
      resolve({
        url,
        status,
        success,
        headers: res.headers
      });
    });
    
    req.on('error', (err) => {
      console.log(`  Error: ${err.message} ✗`);
      resolve({
        url,
        status: 0,
        success: false,
        error: err.message
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.log(`  Timeout ✗`);
      resolve({
        url,
        status: 0,
        success: false,
        error: 'Timeout'
      });
    });
  });
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('Testing static file routing fixes...\n');
  
  const results = [];
  
  for (const url of testUrls) {
    const result = await testUrl(url);
    results.push(result);
    console.log(''); // Empty line for readability
  }
  
  // Summary
  console.log('='.repeat(50));
  console.log('SUMMARY');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`Total tests: ${results.length}`);
  console.log(`Successful: ${successful.length}`);
  console.log(`Failed: ${failed.length}`);
  
  if (failed.length > 0) {
    console.log('\nFailed URLs:');
    failed.forEach(result => {
      console.log(`  ${result.url} - Status: ${result.status} ${result.error ? `(${result.error})` : ''}`);
    });
  }
  
  console.log('\nNote: Some 404 errors are expected if the specific files don\'t exist.');
  console.log('The important thing is that the requests reach the backend and return');
  console.log('proper 404 responses instead of nginx 404 errors.');
  
  return results;
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testUrl, runTests };
