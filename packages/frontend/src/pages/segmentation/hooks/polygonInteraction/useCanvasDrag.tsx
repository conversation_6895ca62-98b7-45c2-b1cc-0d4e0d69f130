import { useCallback } from 'react';

/**
 * Hook pro přesouvání celého plátna
 */
export const useCanvasDrag = (
  zoom: number,
  offset: { x: number; y: number },
  setOffset: (offset: { x: number; y: number }) => void,
  dragState: React.MutableRefObject<{
    isDragging: boolean;
    startX: number;
    startY: number;
    lastX: number;
    lastY: number;
  }>,
) => {
  /**
   * Zpracování pohybu při ta<PERSON>en<PERSON> plátna
   */
  const handleCanvasDrag = useCallback(
    (e: React.MouseEvent, containerElement: HTMLElement): boolean => {
      if (!dragState.current.isDragging) return false;

      const dx = e.clientX - dragState.current.lastX;
      const dy = e.clientY - dragState.current.lastY;

      // Aktualizace last pozice pro plynulý pohyb
      dragState.current.lastX = e.clientX;
      dragState.current.lastY = e.clientY;

      // Upravená metoda pro přesn<PERSON><PERSON><PERSON><PERSON> pohyb při různ<PERSON>ch úrovních zoomu
      setOffset({
        x: offset.x + dx,
        y: offset.y + dy,
      });

      containerElement.style.cursor = 'grabbing';
      return true;
    },
    [zoom, offset, setOffset, dragState],
  );

  /**
   * Začátek tažení plátna
   */
  const startCanvasDrag = useCallback(
    (e: React.MouseEvent) => {
      dragState.current = {
        isDragging: true,
        startX: e.clientX,
        startY: e.clientY,
        lastX: e.clientX,
        lastY: e.clientY,
      };
    },
    [dragState],
  );

  return {
    handleCanvasDrag,
    startCanvasDrag,
  };
};
