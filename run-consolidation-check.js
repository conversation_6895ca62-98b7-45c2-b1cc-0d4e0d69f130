#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to run consolidation checks and identify import inconsistencies
 */

const { ConsolidationChecker } = require('./packages/shared/src/consolidation/consolidationChecker.ts');
const path = require('path');

async function main() {
  const rootDir = process.cwd();
  console.log('Running consolidation check from:', rootDir);
  
  try {
    const checker = new ConsolidationChecker(rootDir);
    const report = await checker.check({
      packages: ['frontend', 'backend', 'shared']
    });
    
    console.log(checker.formatReport(report));
    
    // Save report to file
    await checker.saveReport(report, 'consolidation-report.json');
    console.log('\nReport saved to: consolidation-report.json');
    
    // Exit with error code if there are errors
    if (report.summary.errors > 0) {
      process.exit(1);
    }
  } catch (error) {
    console.error('Error running consolidation check:', error);
    process.exit(1);
  }
}

main();
