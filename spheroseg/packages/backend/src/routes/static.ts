/**
 * Static file serving route
 * 
 * This is a workaround for production where nginx doesn't proxy /uploads
 * It serves static files through the API endpoint
 */

import { Router } from 'express';
import path from 'path';
import fs from 'fs';
import config from '../config';
import logger from '../utils/logger';

const router = Router();

// Serve static files from uploads directory
router.get('/uploads/*', async (req, res) => {
  try {
    // Extract the file path after /api/static/uploads/
    const relativePath = req.path.replace('/uploads/', '');
    const fullPath = path.join(config.storage.uploadDir, relativePath);

    logger.info('Static file request:', {
      requestPath: req.path,
      relativePath,
      fullPath,
      exists: fs.existsSync(fullPath)
    });

    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({
        success: false,
        error: 'File not found',
        path: req.path
      });
    }

    // Check if it's a file (not directory)
    const stats = fs.statSync(fullPath);
    if (!stats.isFile()) {
      return res.status(404).json({
        success: false,
        error: 'Not a file',
        path: req.path
      });
    }

    // Set appropriate content type based on file extension
    const ext = path.extname(fullPath).toLowerCase();
    let contentType = 'application/octet-stream';
    
    if (['.jpg', '.jpeg'].includes(ext)) {
      contentType = 'image/jpeg';
    } else if (ext === '.png') {
      contentType = 'image/png';
    } else if (['.tiff', '.tif'].includes(ext)) {
      contentType = 'image/tiff';
    } else if (ext === '.gif') {
      contentType = 'image/gif';
    } else if (ext === '.webp') {
      contentType = 'image/webp';
    }

    // Set headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // Stream the file
    const stream = fs.createReadStream(fullPath);
    stream.pipe(res);

    stream.on('error', (error) => {
      logger.error('Error streaming file:', {
        path: fullPath,
        error: error.message
      });
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: 'Error reading file'
        });
      }
    });

  } catch (error) {
    logger.error('Static file error:', {
      path: req.path,
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;